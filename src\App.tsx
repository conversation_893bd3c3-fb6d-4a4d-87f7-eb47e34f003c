import { useEffect } from 'react';
import { ThemeProvider } from './components/ThemeProvider';
import { HomePage } from './components/HomePage';
import { notificationService } from './services/notification';
import { useAppStore } from './stores/useAppStore';

function App() {
  const { initialize, records, isLoading } = useAppStore();

  // 初始化通知服务
  useEffect(() => {
    notificationService.initialize();

    // 清理函数
    return () => {
      notificationService.cleanup();
    };
  }, []);

  // 确保应用在启动时进行初始化（双重保险）
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!isLoading && records.length === 0) {
        console.log('App: No records found after initial load, attempting re-initialization');
        initialize().catch(console.error);
      }
    }, 2000); // 2秒后检查

    return () => clearTimeout(timer);
  }, [initialize, isLoading, records.length]);

  return (
    <ThemeProvider>
      <HomePage />
    </ThemeProvider>
  );
}

export default App;
